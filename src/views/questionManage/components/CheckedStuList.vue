<!--
 * @Description: 学生名单
 * @Autor: panmy
 * @Date: 2024-12-05 14:26:41
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 14:48:49
-->
<template>
  <BasicTable :columns="columns" @register="registerSelectedTable">
    <template #tableTitle>
      <a-button type="primary" @click="handleAdd" preIcon="icon-ym icon-ym-btn-add">新增</a-button>
      <a-button type="error" @click="handleDelete" preIcon="icon-ym icon-ym-delete">批量删除</a-button>
      <a-button @click="handleExport" type="link" preIcon="icon-ym icon-ym-btn-download">导出</a-button>
      <a-button @click="handleImport" type="link" preIcon="icon-ym icon-ym-btn-upload">导入</a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <TableAction :actions="getTableActions(record)" :dropDownActions="[]" />
      </template>
    </template>
  </BasicTable>
  <!-- 导入 -->
  <ImportModal @register="registerImportModal" @reload="reload" />
  <!-- 导出 -->
  <ExportModal @register="registerExportModal" />
  <!-- 添加学生 -->
  <StuList @register="registerModal" @reload="reload"></StuList>
</template>
<script lang="ts" setup>
  import { reactive, watch, ref, toRefs, unref, computed, nextTick, onMounted } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import ImportModal from '@/components/CommonModal/src/ImportModal.vue';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';

  import StuList from './StuList.vue';
  import * as schoolApi from '@/api/school';
  const props = defineProps({
    pcdm: String,
  });
  const state = reactive({
    dwdm: '',
    zydm: '',
    nj: '',
    bjdm: '',
  });

  const { t } = useI18n();

  const { createMessage, createConfirm } = useMessage();

  // #region 已选学生数据表格
  const [registerSelectedTable, { reload, setTableData, getForm, getSelectRowKeys }] = useTable({
    columns: [
      { title: '姓名', fixed: 'left', dataIndex: 'xm', width: 80 },
      { title: '学号', fixed: 'left', dataIndex: 'xh', width: 100 },
      { title: '院系', dataIndex: 'dwbzmc', width: 150 },
      { title: '专业', dataIndex: 'zymc', width: 150 },
      { title: '现在年级', dataIndex: 'xznj', width: 80, fixed: 'right' },
      { title: '性别', dataIndex: 'xbmc', width: 80 },
      { title: '出生日期', dataIndex: 'csrq', width: 100 },
      { title: '预计毕业日期', dataIndex: 'yjbyrq', width: 180 },
      { title: '学制', dataIndex: 'xz', width: 100 },
    ],
    clickToRowSelect: true,
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: {
            placeholder: '输入学号/姓名关键字搜索',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '院系',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            clearImmediate: false,

            onChange: (val, obj) => {
              loadZY(val);
            },
          },
        },
        // {
        //   field: 'zydm',
        //   label: '专业',
        //   component: 'Select',
        //   componentProps: {
        //     placeholder: '全部',

        //     onChange: (val, obj) => {
        //       loadNJ(val);
        //     },
        //   },
        // },
        // {
        //   field: 'nj',
        //   label: '年级',
        //   component: 'Select',
        //   componentProps: {
        //     placeholder: '全部',

        //     onChange: (val, obj) => {
        //       loadBJ(val);
        //     },
        //   },
        // },
        // {
        //   field: 'bjdm',
        //   label: '班级',
        //   component: 'Select',
        //   componentProps: {
        //     placeholder: '全部',
        //   },
        // },
      ],
    },
    useSearchForm: true,
    showTableSetting: false,
    // rowSelection: {
    //   type: 'checkbox',
    // },
  });
  // #region 院系 年级  专业 班级
  function loadXY() {
    schoolApi.getXY({ pageSize: 99999 }).then(res => {
      const form = getForm();
      form.updateSchema({ field: 'dwdm', componentProps: { options: res.data.list, fieldNames: { label: 'dwBzmc', value: 'dwDm' } } });
      form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    });
  }
  async function loadZY(dwdm) {
    const form = getForm();
    form.updateSchema({ field: 'zydm', componentProps: { options: [] } });
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ zydm: '', nj: '', bjdm: '' });
    schoolApi.getZY({ dwdm: dwdm, pageSize: 99999 }).then(res => {
      form.updateSchema({ field: 'zydm', componentProps: { options: res.data.list, fieldNames: { label: 'zyMc', value: 'zyDm' } } });
    });
  }
  function loadNJ(zydm) {
    const form = getForm();
    form.updateSchema({ field: 'nj', componentProps: { options: [] } });
    form.setFieldsValue({ nj: '', bjdm: '' });
    const state = form.getFieldsValue();

    schoolApi.getNJ({ dwdm: state.dwdm, zydm: zydm, pageSize: 99999 }).then(res => {
      const data = res.data.list.map(item => {
        return { label: item, value: item };
      });
      form.updateSchema({ field: 'nj', componentProps: { options: data, fieldNames: { label: 'label', value: 'value' } } });
    });
  }

  function loadBJ(nj) {
    const state = getForm().getFieldsValue();
    schoolApi.getBJ({ dwdm: state.dwdm, zydm: state.zydm, nj: nj }).then(res => {
      getForm().updateSchema({ field: 'bjdm', componentProps: { options: res.data.list, fieldNames: { label: 'bjMc', value: 'bjDm' } } });
    });
  }
  //#endregion

  function handleDelete() {
    const ids = getSelectRowKeys();
    if (!ids || ids.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }
    createConfirm({ iconType: 'warning', title: t('common.tipTitle'), content: '确认删除吗？', onOk: () => {} });
  }

  const [registerModal, { openModal }] = useModal();
  function handleAdd() {
    openModal(true, {});
  }
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerExportModal, { openModal: openExportModal }] = useModal();
  function handleExport() {
    openExportModal(true, { columnList: columns });
  }

  function handleImport() {
    openImportModal(true, {});
  }
  function save() {
    const generatedId = 'new-generated-id';
    return {
      success: true,
      id: generatedId,
      errorMessage: '',
    };
  }
  function getDetail() {}
  defineExpose({
    save,
    getDetail,
  });
  onMounted(() => {
    loadXY();
  });
</script>
